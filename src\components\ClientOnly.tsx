'use client';

import { useEffect, useState } from 'react';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Hook for safe date formatting that prevents hydration mismatches
export function useSafeDate() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const formatDate = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    if (!mounted) return '';

    try {
      return new Intl.DateTimeFormat('ar-SA-u-ca-gregory-nu-latn', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return date.toLocaleDateString();
    }
  };

  const formatHijriDate = (date: Date, options?: Intl.DateTimeFormatOptions) => {
    if (!mounted) return '';
    
    try {
      return new Intl.DateTimeFormat('ar-SA-u-ca-islamic-nu-latn', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options
      }).format(date);
    } catch (error) {
      console.error('Error formatting Hijri date:', error);
      return date.toLocaleDateString();
    }
  };

  return { formatDate, formatHijriDate, mounted };
}
